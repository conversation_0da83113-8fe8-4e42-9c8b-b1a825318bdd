// AnySide 内容脚本

console.log('AnySide 内容脚本已加载');

// 监听来自后台脚本的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('内容脚本收到消息:', request);
    
    switch (request.action) {
        case 'getPageContent':
            handleGetPageContent(request, sender, sendResponse);
            break;
            
            
        default:
            console.log('内容脚本: 未知消息类型:', request.action);
    }
    
    return true; // 保持消息通道开放
});

// 获取页面内容
function handleGetPageContent(_request, _sender, sendResponse) {
    try {
        const content = {
            title: document.title,
            url: window.location.href,
            html: document.documentElement.outerHTML,
            text: document.body.innerText,
            images: Array.from(document.images).map(img => ({
                src: img.src,
                alt: img.alt,
                width: img.width,
                height: img.height
            })),
            links: Array.from(document.links).map(link => ({
                href: link.href,
                text: link.textContent.trim(),
                title: link.title
            })),
            meta: {
                description: getMetaContent('description'),
                keywords: getMetaContent('keywords'),
                author: getMetaContent('author'),
                viewport: getMetaContent('viewport')
            }
        };

        sendResponse({ success: true, content });
    } catch (error) {
        console.error('获取页面内容失败:', error);
        sendResponse({ success: false, error: error.message });
    }
}

// 获取meta标签内容
function getMetaContent(name) {
    const meta = document.querySelector(`meta[name="${name}"]`) || 
                 document.querySelector(`meta[property="og:${name}"]`) ||
                 document.querySelector(`meta[property="twitter:${name}"]`);
    return meta ? meta.getAttribute('content') : '';
}

// 页面加载完成后发送通知
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', notifyPageLoaded);
} else {
    notifyPageLoaded();
}

function notifyPageLoaded() {
    chrome.runtime.sendMessage({
        action: 'pageLoaded',
        url: window.location.href,
        title: document.title,
        favicon: getFaviconUrl()
    }).catch(() => {
        // 忽略错误，可能扩展未准备好
    });
}

function getFaviconUrl() {
    const link = document.querySelector("link[rel='icon']") || document.querySelector("link[rel='shortcut icon']");
    return link ? link.href : '';
}

// 监听页面变化（SPA应用）
let lastUrl = window.location.href;
const observer = new MutationObserver(() => {
    if (window.location.href !== lastUrl) {
        lastUrl = window.location.href;
        notifyPageLoaded();
    }
});

observer.observe(document.body, {
    childList: true,
    subtree: true
});

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    observer.disconnect();
});
