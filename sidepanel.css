/* Design System Variables */
:root {
    /* Dark Theme Colors */
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #3a3a3a;
    --bg-accent: #404040;

    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-muted: #808080;

    --border-primary: #404040;
    --border-secondary: #555555;

    --accent-primary: #4a9eff;
    --accent-hover: #3d8bdb;
    --accent-active: #2e6ba8;

    --success: #4caf50;
    --warning: #ff9800;
    --error: #f44336;

    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --spacing-xl: 24px;

    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;

    /* Shadows */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 2px 8px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.5);

    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.2s ease;
    --transition-slow: 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    height: 100vh;
    overflow: hidden;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
}



.tabs-container {
    display: flex;
    align-items: center;
    background-color: var(--bg-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-bottom: 1px solid var(--border-primary);
    overflow-x: auto;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
    min-height: 36px; /* 固定高度确保一致性 */
    gap: var(--spacing-xs);
}

.tabs-container::-webkit-scrollbar {
    display: none;
}

.tabs {
    display: flex;
    flex-grow: 1;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.tabs::-webkit-scrollbar {
    display: none;
}

.tab {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    margin-right: var(--spacing-xs);
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    border-bottom: none;
    border-radius: var(--radius-sm) var(--radius-sm) 0 0;
    cursor: pointer;
    font-size: 12px;
    color: var(--text-secondary);
    min-width: 28px; /* 最小宽度确保图标显示 */
    max-width: 160px; /* 激活标签页可以更宽 */
    height: 28px; /* 固定高度 */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: all var(--transition-normal);
    position: relative;
}

/* 非激活标签页只显示图标，宽度固定 */
.tab:not(.active) {
    width: 28px;
    max-width: 28px;
    justify-content: center;
    padding: var(--spacing-xs);
}

.tab:hover {
    background-color: var(--bg-accent);
    color: var(--text-primary);
}

.tab.active {
    background-color: var(--bg-primary);
    border-color: var(--accent-primary);
    color: var(--text-primary);
    font-weight: 600;
    z-index: 1;
}

.tab-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex-grow: 1;
    padding-right: var(--spacing-sm);
}

.tab-favicon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    border-radius: 2px;
}

.tab-title-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex-grow: 1;
}

/* 非激活标签页隐藏关闭按钮 */
.tab:not(.active) .close-tab-btn {
    display: none;
}

.close-tab-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: 14px;
    cursor: pointer;
    margin-left: var(--spacing-sm);
    padding: 0 2px;
    line-height: 1;
    transition: color var(--transition-normal);
    border-radius: var(--radius-sm);
}

.close-tab-btn:hover {
    color: var(--text-primary);
    background-color: var(--bg-accent);
}

.new-tab-btn {
    background-color: var(--accent-primary);
    color: var(--text-primary);
    border: none;
    border-radius: var(--radius-sm);
    width: 28px;
    height: 28px;
    font-size: 16px;
    cursor: pointer;
    flex-shrink: 0;
    transition: background-color var(--transition-normal);
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
}

.new-tab-btn:hover {
    background-color: var(--accent-hover);
}

.url-input-section {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
}

.navigation-bar {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    width: 100%;
}

.nav-buttons {
    display: flex;
    gap: 2px;
    flex-shrink: 0;
}

.nav-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border: none;
    border-radius: var(--radius-sm);
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.nav-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.nav-btn:active {
    background: var(--bg-accent);
}

.url-input-group {
    display: flex;
    flex: 1;
    gap: var(--spacing-xs);
    align-items: center;
}

.extra-actions {
    display: flex;
    gap: 2px;
    flex-shrink: 0;
}

#urlInput {
    flex: 1;
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-secondary);
    border-radius: var(--radius-sm);
    font-size: 13px;
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    transition: border-color var(--transition-normal), box-shadow var(--transition-normal);
    height: 28px;
    min-width: 0; /* 允许输入框收缩 */
}

#urlInput:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(74, 158, 255, 0.2);
}

#urlInput::placeholder {
    color: var(--text-muted);
}

.load-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    background: var(--accent-primary);
    color: var(--text-primary);
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: background-color var(--transition-normal);
    flex-shrink: 0;
}

.load-btn:hover {
    background: var(--accent-hover);
}

.load-btn:disabled {
    background: var(--bg-accent);
    cursor: not-allowed;
    opacity: 0.6;
}



.content-area {
    flex: 1;
    position: relative;
    background: var(--bg-primary);
    border-top: 1px solid var(--border-primary);
}

#contentFrame {
    width: 100%;
    height: 100%;
    border: none;
    background: var(--bg-primary);
}

.loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 10;
    color: var(--text-primary);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--bg-tertiary);
    border-top: 4px solid var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-lg);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 10;
    background: var(--bg-secondary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-primary);
    max-width: 300px;
}

.error h3 {
    color: var(--error);
    margin-bottom: var(--spacing-sm);
    font-size: 16px;
}

.error p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    font-size: 14px;
    line-height: 1.4;
}

#retryBtn {
    padding: var(--spacing-sm) var(--spacing-lg);
    background: var(--accent-primary);
    color: var(--text-primary);
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-weight: 500;
    transition: background-color var(--transition-normal);
}

#retryBtn:hover {
    background: var(--accent-hover);
}

.status-bar {
    background: var(--bg-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-top: 1px solid var(--border-primary);
    font-size: 11px;
    color: var(--text-muted);
    min-height: 24px;
    display: flex;
    align-items: center;
}

.hidden {
    display: none !important;
}

/* Scrollbar Styling for Dark Theme */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--bg-accent);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--border-secondary);
}

/* Focus styles for accessibility */
button:focus-visible,
input:focus-visible {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
}

/* Animation for smooth transitions */
.tab,
.quick-actions button,
#loadBtn,
.new-tab-btn {
    will-change: background-color, color;
}

/* ===== REUSABLE UI COMPONENTS ===== */

/* Button Components */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-sm);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
    text-decoration: none;
    user-select: none;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: var(--accent-primary);
    color: var(--text-primary);
}

.btn-primary:hover:not(:disabled) {
    background: var(--accent-hover);
}

.btn-primary:active {
    background: var(--accent-active);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-secondary);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--bg-accent);
    color: var(--text-primary);
}

.btn-ghost {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid transparent;
}

.btn-ghost:hover:not(:disabled) {
    background: var(--bg-accent);
    color: var(--text-primary);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 12px;
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 16px;
}

/* Input Components */
.input {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-secondary);
    border-radius: var(--radius-sm);
    font-size: 14px;
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    transition: border-color var(--transition-normal), box-shadow var(--transition-normal);
    width: 100%;
}

.input:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(74, 158, 255, 0.2);
}

.input::placeholder {
    color: var(--text-muted);
}

.input:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background-color: var(--bg-secondary);
}

/* Card Components */
.card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

.card-header {
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-primary);
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.card-subtitle {
    font-size: 14px;
    color: var(--text-secondary);
    margin: var(--spacing-xs) 0 0 0;
}

/* Badge Components */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 12px;
    font-weight: 500;
    line-height: 1;
}

.badge-primary {
    background: var(--accent-primary);
    color: var(--text-primary);
}

.badge-success {
    background: var(--success);
    color: var(--text-primary);
}

.badge-warning {
    background: var(--warning);
    color: var(--text-primary);
}

.badge-error {
    background: var(--error);
    color: var(--text-primary);
}

/* Utility Classes */
.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.gap-xs { gap: var(--spacing-xs); }
.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }

.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }

.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }

.text-sm { font-size: 12px; }
.text-base { font-size: 14px; }
.text-lg { font-size: 16px; }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
