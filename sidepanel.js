// AnySide 侧边栏脚本

class AnySideSidePanel {
    constructor() {
        this.tabs = []; // Array to store tab objects
        this.activeTabId = null; // ID of the currently active tab
        this.isLoading = false;
        
        this.initializeElements();
        this.bindEvents();
        this.loadState(); // Load tabs and active tab from storage
        this.checkPendingUrl();
    }
    
    initializeElements() {
        this.urlInput = document.getElementById('urlInput');
        this.loadBtn = document.getElementById('loadBtn');
        this.refreshBtn = document.getElementById('refreshBtn');
        this.backBtn = document.getElementById('backBtn');
        this.forwardBtn = document.getElementById('forwardBtn');
        this.homeBtn = document.getElementById('homeBtn');
        this.contentFrame = document.getElementById('contentFrame');
        this.loadingIndicator = document.getElementById('loadingIndicator');
        this.errorMessage = document.getElementById('errorMessage');
        this.errorText = document.getElementById('errorText');
        this.retryBtn = document.getElementById('retryBtn');
        this.statusText = document.getElementById('statusText');

        // Tab related elements
        this.tabsContainer = document.getElementById('tabs');
        this.newTabBtn = document.getElementById('newTabBtn');
    }
    
    bindEvents() {
        // URL输入和加载
        this.loadBtn.addEventListener('click', () => this.loadUrl());
        this.urlInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.loadUrl();
        });
        
        // 导航按钮
        this.refreshBtn.addEventListener('click', () => this.refresh());
        this.backBtn.addEventListener('click', () => this.goBack());
        this.forwardBtn.addEventListener('click', () => this.goForward());
        this.homeBtn.addEventListener('click', () => this.goHome());
        this.retryBtn.addEventListener('click', () => this.retry());
        
        // iframe事件
        this.contentFrame.addEventListener('load', () => this.onFrameLoad());
        this.contentFrame.addEventListener('error', () => this.onFrameError());
        
        // 监听来自后台脚本的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
        });

        // Tab related events
        this.newTabBtn.addEventListener('click', () => this.addNewTab());
        this.tabsContainer.addEventListener('click', (e) => {
            const clickedTab = e.target.closest('.tab');
            if (clickedTab) {
                const tabId = clickedTab.dataset.tabId;
                if (e.target.classList.contains('close-tab-btn')) {
                    this.closeTab(tabId);
                } else {
                    this.activateTab(tabId);
                }
            }
        });
    }

    async loadState() {
        console.log('尝试加载侧边栏状态...');
        try {
            const storedState = await chrome.storage.local.get(['tabs', 'activeTabId']);
            if (storedState.tabs && storedState.tabs.length > 0) {
                this.tabs = storedState.tabs;
                this.activeTabId = storedState.activeTabId || this.tabs[0].id;
                console.log('已加载存储的标签页:', this.tabs);
            } else {
                console.log('未找到存储的标签页，创建默认标签页...');
                // If no stored tabs, create a default one
                await this.addNewTab(true); // true for initial load, don't save state yet
            }
            this.renderTabs();
            this.activateTab(this.activeTabId, true); // Activate with reloading iframe
            console.log('侧边栏状态加载完成。');
        } catch (error) {
            console.error('加载状态失败:', error);
            // Fallback to creating a new tab if loading fails
            await this.addNewTab(true);
            this.renderTabs();
            this.activateTab(this.activeTabId, true);
        }
    }

    async saveState() {
        try {
            await chrome.storage.local.set({
                tabs: this.tabs,
                activeTabId: this.activeTabId
            });
        } catch (error) {
            console.error('保存状态失败:', error);
        }
    }
    
    async checkPendingUrl() {
        try {
            const data = await chrome.storage.local.get(['pendingUrl', 'loadTimestamp']);
            if (data.pendingUrl && data.loadTimestamp) {
                // 检查时间戳，避免加载过期的URL
                const now = Date.now();
                if (now - data.loadTimestamp < 30000) { // 30秒内有效
                    // Load URL in a new tab if there's a pending URL
                    await this.addNewTab(false, data.pendingUrl);
                    // Clear pending URL
                    chrome.storage.local.remove(['pendingUrl', 'loadTimestamp']);
                }
            }
        } catch (error) {
            console.error('检查待加载URL失败:', error);
        }
    }
    
    loadUrl(url = null) {
        const targetUrl = url || this.urlInput.value.trim();
        
        if (!targetUrl) {
            this.showError('请输入有效的URL');
            return;
        }
        
        const activeTab = this.getActiveTab();
        if (!activeTab) {
            console.error('没有活动的标签页');
            return;
        }

        // 确保URL有协议
        const finalUrl = this.normalizeUrl(targetUrl);
        
        this.showLoading();
        this.updateStatus('正在加载...');
        
        // 更新历史记录
        if (finalUrl !== activeTab.currentUrl) {
            this.addToHistory(activeTab, finalUrl);
        }
        
        activeTab.currentUrl = finalUrl;
        this.urlInput.value = finalUrl;
        
        // 加载到iframe
        this.contentFrame.src = finalUrl;
        
        // 更新导航按钮状态
        this.updateNavigationButtons();
        this.updateTabTitle(activeTab.id, finalUrl);
        this.saveState();
    }
    
    normalizeUrl(url) {
        // 如果没有协议，默认添加https://
        if (!/^https?:\/\//i.test(url)) {
            // 检查是否是本地文件或特殊协议
            if (url.startsWith('file://') || url.startsWith('chrome://') || url.startsWith('moz-extension://')) {
                return url;
            }
            return 'https://' + url;
        }
        return url;
    }
    
    addToHistory(tab, url) {
        // 移除当前位置之后的历史记录
        tab.history = tab.history.slice(0, tab.historyIndex + 1);
        tab.history.push(url);
        tab.historyIndex = tab.history.length - 1;
        
        // 限制历史记录长度
        if (tab.history.length > 50) {
            tab.history.shift();
            tab.historyIndex--;
        }
    }
    
    refresh() {
        const activeTab = this.getActiveTab();
        if (activeTab && activeTab.currentUrl) {
            this.showLoading();
            this.updateStatus('正在刷新...');
            this.contentFrame.src = activeTab.currentUrl;
        }
    }
    
    goBack() {
        const activeTab = this.getActiveTab();
        if (activeTab && activeTab.historyIndex > 0) {
            activeTab.historyIndex--;
            const url = activeTab.history[activeTab.historyIndex];
            activeTab.currentUrl = url;
            this.urlInput.value = url;
            this.contentFrame.src = url;
            this.updateNavigationButtons();
            this.updateStatus('后退到: ' + this.getDisplayUrl(url));
            this.updateTabTitle(activeTab.id, url);
            this.saveState();
        }
    }
    
    goForward() {
        const activeTab = this.getActiveTab();
        if (activeTab && activeTab.historyIndex < activeTab.history.length - 1) {
            activeTab.historyIndex++;
            const url = activeTab.history[activeTab.historyIndex];
            activeTab.currentUrl = url;
            this.urlInput.value = url;
            this.contentFrame.src = url;
            this.updateNavigationButtons();
            this.updateStatus('前进到: ' + this.getDisplayUrl(url));
            this.updateTabTitle(activeTab.id, url);
            this.saveState();
        }
    }
    
    async goHome() {
        try {
            const settings = await chrome.storage.sync.get(['defaultUrl']);
            const homeUrl = settings.defaultUrl || 'https://www.google.com';
            this.loadUrl(homeUrl);
        } catch (error) {
            this.loadUrl('https://www.google.com');
        }
    }
    
    retry() {
        const activeTab = this.getActiveTab();
        if (activeTab && activeTab.currentUrl) {
            this.hideError();
            this.loadUrl(activeTab.currentUrl);
        }
    }
    
    onFrameLoad() {
        this.hideLoading();
        this.hideError();
        this.isLoading = false;
        
        const activeTab = this.getActiveTab();
        if (!activeTab) return;

        try {
            // 尝试获取iframe的URL（可能因为跨域限制而失败）
            const frameUrl = this.contentFrame.contentWindow.location.href;
            if (frameUrl && frameUrl !== 'about:blank' && frameUrl !== activeTab.currentUrl) {
                activeTab.currentUrl = frameUrl;
                this.urlInput.value = frameUrl;
                this.addToHistory(activeTab, frameUrl);
            }

            // 获取页面标题和图标
            try {
                const doc = this.contentFrame.contentWindow.document;
                const pageTitle = doc.title;
                const favicon = doc.querySelector('link[rel="shortcut icon"], link[rel="icon"]')?.href;
                
                if (pageTitle) {
                    this.updateTabInfo(frameUrl, pageTitle, favicon || `${new URL(frameUrl).origin}/favicon.ico`);
                }
            } catch (error) {
                console.log('无法获取iframe内容:', error);
                this.updateTabInfo(frameUrl, this.getDisplayUrl(frameUrl));
            }
        } catch (error) {
            // 跨域限制，忽略错误
        }
        
        this.updateStatus('加载完成: ' + this.getDisplayUrl(activeTab.currentUrl));
        this.updateNavigationButtons();
        this.updateTabTitle(activeTab.id, activeTab.currentUrl);
        this.saveState();
    }
    
    onFrameError() {
        this.hideLoading();
        this.isLoading = false;
        this.showError('无法加载页面，可能是网络问题或页面不允许在iframe中显示');
        this.updateStatus('加载失败');
        this.saveState();
    }
    
    showLoading() {
        this.isLoading = true;
        this.loadingIndicator.classList.remove('hidden');
        this.errorMessage.classList.add('hidden');
        this.loadBtn.disabled = true;
    }
    
    hideLoading() {
        this.loadingIndicator.classList.add('hidden');
        this.loadBtn.disabled = false;
    }
    
    showError(message) {
        this.errorText.textContent = message;
        this.errorMessage.classList.remove('hidden');
        this.hideLoading();
    }
    
    hideError() {
        this.errorMessage.classList.add('hidden');
    }
    
    updateStatus(message) {
        this.statusText.textContent = message;
    }
    
    updateNavigationButtons() {
        const activeTab = this.getActiveTab();
        if (activeTab) {
            this.backBtn.disabled = activeTab.historyIndex <= 0;
            this.forwardBtn.disabled = activeTab.historyIndex >= activeTab.history.length - 1;
        } else {
            this.backBtn.disabled = true;
            this.forwardBtn.disabled = true;
        }
    }
    
    getDisplayUrl(url) {
        try {
            const urlObj = new URL(url);
            return urlObj.hostname + urlObj.pathname;
        } catch {
            return url.length > 50 ? url.substring(0, 50) + '...' : url;
        }
    }

    // Tab Management Functions
    generateTabId() {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    }

    getActiveTab() {
        return this.tabs.find(tab => tab.id === this.activeTabId);
    }

    async addNewTab(isInitialLoad = false, url = 'https://www.google.com') {
        console.log(`添加新标签页。初始加载: ${isInitialLoad}, URL: ${url}`);
        const newTabId = this.generateTabId();
        const newTab = {
            id: newTabId,
            title: '新标签页',
            currentUrl: url,
            history: [url],
            historyIndex: 0
        };
        this.tabs.push(newTab);
        this.activeTabId = newTabId;
        this.renderTabs();
        // Always load iframe for new tabs or when activating from storage
        this.activateTab(newTabId, true);
        if (!isInitialLoad) {
            this.saveState();
        }
        console.log('新标签页已添加:', newTab);
    }

    activateTab(tabId, loadIframe = true) {
        console.log(`激活标签页: ${tabId}, 是否加载iframe: ${loadIframe}`);
        this.activeTabId = tabId;
        this.renderTabs(); // Re-render to update active class
        const activeTab = this.getActiveTab();
        if (activeTab) {
            this.urlInput.value = activeTab.currentUrl;
            if (loadIframe) {
                console.log(`加载iframe src: ${activeTab.currentUrl}`);
                this.contentFrame.src = activeTab.currentUrl;
            } else {
                console.log('不加载iframe，仅更新URL输入框。');
                this.contentFrame.src = 'about:blank'; // Clear iframe content if not loading
            }
            this.updateNavigationButtons();
            this.updateStatus('切换到: ' + this.getDisplayUrl(activeTab.currentUrl));
        }
        this.saveState();
    }

    closeTab(tabId) {
        const index = this.tabs.findIndex(tab => tab.id === tabId);
        if (index > -1) {
            this.tabs.splice(index, 1);
            if (this.tabs.length === 0) {
                // If no tabs left, create a new one
                this.addNewTab();
            } else if (tabId === this.activeTabId) {
                // If closing active tab, activate an adjacent one
                const newActiveIndex = Math.min(index, this.tabs.length - 1);
                this.activateTab(this.tabs[newActiveIndex].id);
            } else {
                this.renderTabs(); // Just re-render if non-active tab closed
            }
            this.saveState();
        }
    }

    renderTabs() {
        this.tabsContainer.innerHTML = '';
        this.tabs.forEach(tab => {
            const tabElement = document.createElement('div');
            tabElement.classList.add('tab');
            if (tab.id === this.activeTabId) {
                tabElement.classList.add('active');
            }
            tabElement.dataset.tabId = tab.id;

            const titleSpan = document.createElement('span');
            titleSpan.classList.add('tab-title');

            // 添加图标显示
            if(tab.favicon) {
                const faviconImg = document.createElement('img');
                faviconImg.classList.add('tab-favicon');
                faviconImg.src = tab.favicon;
                faviconImg.onerror = function() {
                    // 如果图标加载失败，使用默认图标
                    this.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMTVBNyA3IDAgMSAwIDggMWE3IDcgMCAwIDAgMCAxNFoiIGZpbGw9IiM2NjY2NjYiLz4KPHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMTVBNyA3IDAgMSAwIDggMWE3IDcgMCAwIDAgMCAxNFoiIGZpbGw9IiM2NjY2NjYiLz4KPC9zdmc+';
                };
                titleSpan.appendChild(faviconImg);
            } else {
                // 如果没有图标，使用默认图标
                const defaultIcon = document.createElement('img');
                defaultIcon.classList.add('tab-favicon');
                defaultIcon.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMTVBNyA3IDAgMSAwIDggMWE3IDcgMCAwIDAgMCAxNFoiIGZpbGw9IiM2NjY2NjYiLz4KPC9zdmc+';
                titleSpan.appendChild(defaultIcon);
            }

            // 只有激活的标签页才显示标题文本
            if (tab.id === this.activeTabId) {
                const titleText = document.createElement('span');
                titleText.classList.add('tab-title-text');
                titleText.textContent = tab.title;
                titleSpan.appendChild(titleText);
            }

            tabElement.appendChild(titleSpan);

            const closeButton = document.createElement('button');
            closeButton.classList.add('close-tab-btn');
            closeButton.textContent = 'x';
            tabElement.appendChild(closeButton);

            this.tabsContainer.appendChild(tabElement);
        });
    }

    updateTabTitle(tabId, url, title) {
        const tab = this.tabs.find(t => t.id === tabId);
        if (tab) {
            // 优先使用网页标题，没有时使用智能域名显示
            tab.title = title || this.getDisplayUrl(url);
            this.renderTabs();
        }
    }

    updateTabInfo(url, title, favicon) {
        const activeTab = this.getActiveTab();
        if (activeTab) {
            activeTab.title = title;
            activeTab.favicon = favicon;
            this.renderTabs();
            this.updateFavicon(favicon);
            this.updateTabTitle(activeTab.id, url, title);
        }
    }

    updateFavicon(faviconUrl) {
        let faviconLink = document.querySelector("link[rel='shortcut icon']");
        if (!faviconLink) {
            faviconLink = document.createElement('link');
            faviconLink.rel = 'shortcut icon';
            document.head.appendChild(faviconLink);
        }
        faviconLink.href = faviconUrl;
    }
    
    handleMessage(request, _sender, sendResponse) {
        console.log('侧边栏收到消息:', request);

        switch (request.action) {
            case 'loadUrlInSidePanel':
                this.addNewTab(false, request.url);
                sendResponse({ success: true });
                break;

            case 'tabUpdated':
                // 标签页更新处理逻辑可在此添加
                console.log('标签页已更新:', request.tab);
                break;

            case 'activeTabChanged':
                // 活动标签页改变处理逻辑可在此添加
                console.log('活动标签页已改变:', request.tab);
                break;

            case 'pageLoaded':
                this.updateTabInfo(request.url, request.title, request.favicon);
                break;

            default:
                console.log('侧边栏: 未知消息类型:', request.action);
        }
    }
}

// 初始化侧边栏
document.addEventListener('DOMContentLoaded', () => {
    new AnySideSidePanel();
});