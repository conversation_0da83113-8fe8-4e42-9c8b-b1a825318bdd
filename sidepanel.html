<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AnySide</title>
    <link rel="stylesheet" href="sidepanel.css">
</head>
<body>
    <div class="container">
        <div class="tabs-container">
            <div id="tabs" class="tabs">
                <!-- Tabs will be injected here by JavaScript -->
            </div>
            <button id="newTabBtn" class="new-tab-btn" title="新标签页">+</button>
        </div>
        
        <div class="url-input-section">
            <div class="navigation-bar">
                <div class="nav-buttons">
                    <button id="backBtn" class="nav-btn" title="后退">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M8.5 2.5L3 8l5.5 5.5L9.5 12 5.5 8l4-4z"/>
                        </svg>
                    </button>
                    <button id="forwardBtn" class="nav-btn" title="前进">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M7.5 2.5L13 8l-5.5 5.5L6.5 12l4-4-4-4z"/>
                        </svg>
                    </button>
                    <button id="refreshBtn" class="nav-btn" title="刷新">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2v1z"/>
                            <path d="M8 4.466V.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 0 1 8 4.466z"/>
                        </svg>
                    </button>
                </div>
                <div class="url-input-group">
                    <input type="url" id="urlInput" placeholder="输入网页URL..." />
                    <button id="loadBtn" class="load-btn">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0zM4.5 7.5a.5.5 0 0 0 0 1h5.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H4.5z"/>
                        </svg>
                    </button>
                </div>
                <div class="extra-actions">
                    <button id="homeBtn" class="nav-btn" title="主页">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M8.354 1.146a.5.5 0 0 0-.708 0l-6 6A.5.5 0 0 0 1.5 7.5v7a.5.5 0 0 0 .5.5h4.5a.5.5 0 0 0 .5-.5v-4h2v4a.5.5 0 0 0 .5.5H14a.5.5 0 0 0 .5-.5v-7a.5.5 0 0 0-.146-.354L8.354 1.146zM2.5 14V7.707l5.5-5.5 5.5 5.5V14H10v-4a.5.5 0 0 0-.5-.5h-3a.5.5 0 0 0-.5.5v4H2.5z"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <div class="content-area">
            <div id="loadingIndicator" class="loading hidden">
                <div class="spinner"></div>
                <p>正在加载...</p>
            </div>
            
            <iframe id="contentFrame"
                    src=""
                    frameborder="0"
                    sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-top-navigation">
            </iframe>
            
            <div id="errorMessage" class="error hidden">
                <h3>无法加载页面</h3>
                <p id="errorText"></p>
                <button id="retryBtn">重试</button>
            </div>
        </div>

        <div class="status-bar">
            <span id="statusText">就绪</span>
        </div>
    </div>

    <script src="sidepanel.js"></script>
</body>
</html>
