{"manifest_version": 3, "name": "AnySide", "version": "1.0.0", "description": "在浏览器侧边栏中展示任何网页内容", "permissions": ["sidePanel", "activeTab", "storage", "tabs", "declarativeNetRequest", "scripting"], "host_permissions": ["http://*/*", "https://*/*"], "background": {"service_worker": "background.js"}, "declarative_net_request": {"rule_resources": [{"id": "rules", "enabled": true, "path": "rules.json"}]}, "side_panel": {"default_path": "sidepanel.html"}, "action": {"default_title": "AnySide", "default_icon": {"16": "icons/icon.svg", "32": "icons/icon.svg", "48": "icons/icon.svg", "128": "icons/icon.svg"}}, "icons": {"16": "icons/icon.svg", "32": "icons/icon.svg", "48": "icons/icon.svg", "128": "icons/icon.svg"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"], "run_at": "document_end"}]}