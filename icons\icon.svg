<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆角矩形 -->
  <rect x="8" y="8" width="112" height="112" rx="16" ry="16" fill="url(#bgGradient)"/>
  
  <!-- 侧边栏图标 -->
  <!-- 左侧栏 -->
  <rect x="20" y="30" width="8" height="68" fill="white"/>
  
  <!-- 主内容区 -->
  <rect x="36" y="30" width="72" height="68" fill="white"/>
  
  <!-- 装饰线条 -->
  <rect x="44" y="40" width="56" height="2" fill="rgba(255,255,255,0.6)"/>
  <rect x="44" y="50" width="40" height="2" fill="rgba(255,255,255,0.6)"/>
  <rect x="44" y="60" width="48" height="2" fill="rgba(255,255,255,0.6)"/>
  <rect x="44" y="70" width="32" height="2" fill="rgba(255,255,255,0.6)"/>
  <rect x="44" y="80" width="44" height="2" fill="rgba(255,255,255,0.6)"/>
</svg>
