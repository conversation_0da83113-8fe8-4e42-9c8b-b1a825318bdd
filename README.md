# AnySide - Chrome 侧边栏网页浏览器

AnySide 是一个 Chrome 扩展，允许您在浏览器的侧边栏中展示和浏览任何网页内容。

## 功能特性

- 🌐 **任意网页浏览**: 在侧边栏中加载和浏览任何网页
- 📱 **侧边栏集成**: 使用 Chrome 原生侧边栏 API，体验流畅
- 🔄 **完整导航**: 支持前进、后退、刷新、主页等导航功能
- ⚙️ **个性化设置**: 可自定义默认主页、主题、缩放等
- 🚀 **快速访问**: 一键在侧边栏中加载当前页面
- 🎨 **美观界面**: 现代化的用户界面设计

## 安装方法

### 开发者模式安装

1. 打开 Chrome 浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 AnySide 项目文件夹
6. 扩展安装完成！

## 使用方法

### 基本使用

1. **打开侧边栏**: 点击工具栏中的 AnySide 图标
2. **输入网址**: 在侧边栏顶部的地址栏中输入要访问的网址
3. **开始浏览**: 点击"加载"按钮或按回车键

### 快捷操作

- **加载当前页面**: 点击扩展图标，然后点击"在侧边栏中加载当前页面"
- **导航控制**: 使用侧边栏中的前进、后退、刷新、主页按钮
- **快速设置**: 点击扩展弹窗中的"设置"按钮

### 设置选项

访问设置页面可以配置：

- **默认主页**: 设置侧边栏打开时的默认页面
- **自动加载**: 打开侧边栏时自动加载默认主页
- **主题选择**: 浅色、深色或跟随系统主题
- **缩放级别**: 调整侧边栏中网页的显示比例
- **弹窗阻止**: 阻止侧边栏中的网页打开新窗口

## 技术特性

### 支持的功能

- ✅ HTTP/HTTPS 网站浏览
- ✅ 响应式网页适配
- ✅ JavaScript 执行
- ✅ 表单提交
- ✅ Cookie 和会话管理
- ✅ 多媒体内容播放

### 限制说明

- ❌ 某些网站可能禁止在 iframe 中显示（如银行网站）
- ❌ Chrome 内部页面（chrome://）无法加载
- ❌ 部分需要特殊权限的功能可能受限

## 文件结构

```
AnySide/
├── manifest.json          # 扩展配置文件
├── background.js          # 后台服务脚本
├── content.js            # 内容脚本
├── sidepanel.html        # 侧边栏页面
├── sidepanel.css         # 侧边栏样式
├── sidepanel.js          # 侧边栏脚本
├── popup.html            # 弹窗页面
├── popup.js              # 弹窗脚本
├── settings.html         # 设置页面
├── settings.js           # 设置脚本
├── icons/                # 图标文件夹
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   ├── icon128.png
│   └── create_icons.html # 图标生成器
└── README.md             # 说明文档
```

## 开发说明

### 核心 API

- **Chrome Side Panel API**: 用于创建和管理侧边栏
- **Chrome Storage API**: 用于保存用户设置
- **Chrome Tabs API**: 用于获取当前标签页信息
- **Chrome Runtime API**: 用于消息传递

### 主要组件

1. **Background Script**: 处理扩展生命周期和消息路由
2. **Side Panel**: 主要的网页浏览界面
3. **Popup**: 快速操作界面
4. **Content Script**: 页面内容获取和交互
5. **Settings**: 用户配置管理

## 故障排除

### 常见问题

**Q: 某些网站无法在侧边栏中显示**
A: 这是正常现象，某些网站出于安全考虑禁止在 iframe 中显示。

**Q: 页面加载很慢**
A: 可能是网络问题或目标网站响应慢，请尝试刷新或更换网络。

**Q: 设置无法保存**
A: 请检查 Chrome 的存储权限，或尝试重新安装扩展。

### 调试方法

1. 打开 Chrome 开发者工具
2. 在 Console 中查看错误信息
3. 检查 Network 标签页的网络请求
4. 查看 Application > Storage 中的扩展数据

## 更新日志

### v1.0.0
- 初始版本发布
- 基本的侧边栏浏览功能
- 用户设置和主题支持
- 导航控制和历史记录

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！

## 联系方式

如有问题或建议，请通过 GitHub Issues 联系我们。
